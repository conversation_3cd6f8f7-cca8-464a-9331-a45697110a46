use crate::dto::ads::test_item_detail::TestItemDetail;
use crate::model::constant::{EMPTY, M, P, PF_PASS, ZERO};
use crate::utils::date::{get_day, get_day_hour};
use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};

use crate::dto::ads::{group_detail, mk_string_distinct};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;

/// ADS TestItemSiteBin aggregation result structure
/// Contains statistical analysis aggregated at both site and bin levels
/// Corresponds to ads_yms_stage_test_item_program_site_bin_cluster table
#[derive(Debug, Clone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemSiteBin {
    // Data source and upload information
    pub DATA_SOURCE: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,

    // Factory and location information
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,

    // Product and lot information
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PRODUCT: Arc<str>,
    pub PRODUCT_TYPE: Arc<str>,
    pub PRODUCT_FAMILY: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,

    // Site information
    pub SITE: Option<u32>,

    // Bin information
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NAM: Arc<str>,
    pub SBIN_PF: Arc<str>,

    // Processing flags
    pub IS_PASS_ONLY: u8,
    pub IS_FINAL: u8,

    // List fields for aggregated data
    pub UNITS_LIST: Arc<str>,
    pub ORIGIN_UNITS_LIST: Arc<str>,
    pub LO_LIMIT_LIST: Arc<str>,
    pub HI_LIMIT_LIST: Arc<str>,
    pub ORIGIN_LO_LIMIT_LIST: Arc<str>,
    pub ORIGIN_HI_LIMIT_LIST: Arc<str>,
    pub PROCESS_LIST: Arc<str>,
    pub TESTITEM_TYPE_LIST: Arc<str>,
    pub TEST_TEMPERATURE_LIST: Arc<str>,
    pub TESTER_NAME_LIST: Arc<str>,
    pub TESTER_TYPE_LIST: Arc<str>,
    pub PROBER_HANDLER_TYP_LIST: Arc<str>,
    pub PROBER_HANDLER_ID_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_ID_LIST: Arc<str>,
    pub RETEST_BIN_NUM_LIST: Arc<str>,

    // Count fields
    pub INPUT_CNT: i32,
    pub PASS_CNT: i32,
    pub FAIL_CNT: i32,
    pub PASSBIN_FAILINGITEM_CNT: i32,
    pub EXE_INPUT_CNT: i32,
    pub EXE_PASS_CNT: i32,
    pub EXE_FAIL_CNT: i32,

    // Statistical metrics - basic
    pub MEDIAN: Option<Decimal38_18>,
    pub MEAN: Option<Decimal38_18>,
    pub MAX: Option<Decimal38_18>,
    pub MIN: Option<Decimal38_18>,
    pub MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub SUM_SQ: Option<Decimal38_18>,
    pub SUM_VALUE: Option<Decimal38_18>,
    pub STDEV_P: Option<Decimal38_18>,
    pub STDEV_S: Option<Decimal38_18>,
    pub RANGE: Option<Decimal38_18>,
    pub IQR: Option<Decimal38_18>,

    // Quantiles
    pub Q1: Option<Decimal38_18>,
    pub Q3: Option<Decimal38_18>,
    pub LOWER: Option<Decimal38_18>,
    pub UPPER: Option<Decimal38_18>,
    pub OUTLIER_CNT: u32,
    pub P1: Option<Decimal38_18>,
    pub P5: Option<Decimal38_18>,
    pub P10: Option<Decimal38_18>,
    pub P90: Option<Decimal38_18>,
    pub P95: Option<Decimal38_18>,
    pub P99: Option<Decimal38_18>,

    // Histogram data
    pub GROUP_DETAIL: Vec<(String, u32)>,

    // Process capability indices
    pub PP: Option<Decimal38_18>,
    pub PPU: Option<Decimal38_18>,
    pub PPL: Option<Decimal38_18>,
    pub PPK: Option<Decimal38_18>,
    pub CP: Option<Decimal38_18>,
    pub CPU: Option<Decimal38_18>,
    pub CPL: Option<Decimal38_18>,
    pub CPK: Option<Decimal38_18>,
    pub CA: Option<Decimal38_18>,

    // Advanced statistical metrics
    pub SKEWNESS: Option<Decimal38_18>,
    pub KURTOSIS: Option<Decimal38_18>,

    // Normalization metrics
    pub NORMALIZATION_MEDIAN: Option<Decimal38_18>,
    pub NORMALIZATION_MEAN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX: Option<Decimal38_18>,
    pub NORMALIZATION_MIN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_IQR: Option<Decimal38_18>,
    pub NORMALIZATION_Q1: Option<Decimal38_18>,
    pub NORMALIZATION_Q3: Option<Decimal38_18>,
    pub NORMALIZATION_LOWER: Option<Decimal38_18>,
    pub NORMALIZATION_UPPER: Option<Decimal38_18>,

    // Timestamps
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,

    // System fields
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}
impl TestItemSiteBin {
    /// Creates a new TestItemSiteBin from a list of TestItemDetail items
    /// This is the equivalent of the Scala adsTestItemSiteBin method
    pub fn from_test_items(
        is_cp: bool,
        data_source: &str,
        items: &Vec<Arc<TestItemDetail>>,
        is_pass_only: u8,
        product_list: &[crate::dto::ods::product_config::OdsProductConfig],
    ) -> Self {
        let item = &items[0]; // Get first item for common fields
        let testitem_type = item.TESTITEM_TYPE.as_ref();

        // Filter test values for statistical calculations
        let test_values: Vec<f64> = items
            .iter()
            .filter(|t| t.TEST_VALUE.is_some() && t.TEST_RESULT.is_some() && t.TEST_RESULT.unwrap() < 2)
            .map(|t| t.TEST_VALUE.unwrap())
            .collect();

        // Get product information
        let (product, product_type, product_family) = if !product_list.is_empty() {
            let product_info = &product_list[0];
            (
                if product_info.PRODUCT.is_empty() { EMPTY } else { product_info.PRODUCT.as_ref() },
                if product_info.PRODUCT_TYPE.is_empty() { EMPTY } else { product_info.PRODUCT_TYPE.as_ref() },
                if product_info.PRODUCT_FAMILY.is_empty() { EMPTY } else { product_info.PRODUCT_FAMILY.as_ref() },
            )
        } else {
            (EMPTY, EMPTY, EMPTY)
        };

        let now = Utc::now();
        let create_hour_key = get_day_hour(now);
        let create_day_key = get_day(now);

        // Build distinct lists
        let sblot_id_arr = mk_string_distinct(items.iter().map(|i| i.SBLOT_ID.as_ref()).collect());
        let wafer_no_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_NO.as_ref()).collect());
        let wafer_id_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_ID.as_ref()).collect());
        let wafer_lot_id_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_LOT_ID.as_ref()).collect());
        let units_arr = mk_string_distinct(items.iter().map(|i| i.UNITS.as_ref()).collect());
        let origin_units_arr = mk_string_distinct(items.iter().map(|i| i.ORIGIN_UNITS.as_ref()).collect());
        let lo_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.LO_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let lo_limit_arr = mk_string_distinct(lo_limit_strings.iter().map(|s| s.as_str()).collect());

        let hi_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.HI_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let hi_limit_arr = mk_string_distinct(hi_limit_strings.iter().map(|s| s.as_str()).collect());

        let origin_lo_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.ORIGIN_LO_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let origin_lo_limit_arr = mk_string_distinct(origin_lo_limit_strings.iter().map(|s| s.as_str()).collect());

        let origin_hi_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.ORIGIN_HI_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let origin_hi_limit_arr = mk_string_distinct(origin_hi_limit_strings.iter().map(|s| s.as_str()).collect());
        let process_arr = mk_string_distinct(items.iter().map(|i| i.PROCESS.as_ref()).collect());
        let testitem_type_arr = mk_string_distinct(items.iter().map(|i| i.TESTITEM_TYPE.as_ref()).collect());
        let test_temperature_arr = mk_string_distinct(items.iter().map(|i| i.TEST_TEMPERATURE.as_ref()).collect());
        let tester_name_arr = mk_string_distinct(items.iter().map(|i| i.TESTER_NAME.as_ref()).collect());
        let tester_type_arr = mk_string_distinct(items.iter().map(|i| i.TESTER_TYPE.as_ref()).collect());
        let prober_handler_id_arr = mk_string_distinct(items.iter().map(|i| i.PROBER_HANDLER_ID.as_ref()).collect());
        let probecard_loadboard_id_arr =
            mk_string_distinct(items.iter().map(|i| i.PROBECARD_LOADBOARD_ID.as_ref()).collect());
        let prober_handler_typ_arr = mk_string_distinct(items.iter().map(|i| i.PROBER_HANDLER_TYP.as_ref()).collect());
        let probecard_loadboard_typ_arr =
            mk_string_distinct(items.iter().map(|i| i.PROBECARD_LOADBOARD_TYP.as_ref()).collect());
        let retest_bin_num_arr = mk_string_distinct(items.iter().map(|i| i.RETEST_BIN_NUM.as_ref()).collect());

        let sblot_id = if is_cp { sblot_id_arr.as_str() } else { item.SBLOT_ID.as_ref() };
        let wafer_no = if is_cp { item.WAFER_NO.as_ref() } else { wafer_no_arr.as_str() };

        // Calculate counts
        let standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();

        let input_cnt = (standard_ecids.len() + non_standard_ecids.len()) as i32;

        let pass_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let pass_non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();

        let pass_cnt = (pass_standard_ecids.len() + pass_non_standard_ecids.len()) as i32;

        let fail_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .map(|i| i.ECID.as_ref())
            .collect();
        let fail_non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .map(|i| i.ECID.as_ref())
            .collect();

        let fail_cnt = (fail_standard_ecids.len() + fail_non_standard_ecids.len()) as i32;

        let passbin_failing_item_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1) && i.HBIN_PF.as_ref() == PF_PASS)
            .map(|i| i.ECID.as_ref())
            .collect();
        let passbin_failing_item_non_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1) && i.HBIN_PF.as_ref() == PF_PASS)
            .map(|i| i.ECID.as_ref())
            .collect();

        let passbin_failingitem_cnt =
            (passbin_failing_item_standard.len() + passbin_failing_item_non_standard.len()) as i32;

        let exe_input_cnt = items.iter().filter(|i| i.TEST_VALUE.is_some()).count() as i32;

        let exe_pass_cnt = items.iter().filter(|i| i.TEST_RESULT == Some(1)).count() as i32;

        let exe_fail_cnt = items
            .iter()
            .filter(|i| i.TEST_RESULT.is_some() && i.TEST_RESULT != Some(1))
            .count() as i32;

        // Calculate time ranges - each field calculated independently as in Scala version
        let first_item = items
            .iter()
            .filter(|item| item.START_TIME.is_some())
            .min_by_key(|item| item.START_TIME)
            .ok_or_else(|| {
                panic!("No item with START_TIME found");
            })
            .unwrap();
        let last_item = items
            .iter()
            .filter(|item| item.END_TIME.is_some())
            .max_by_key(|item| item.END_TIME)
            .ok_or_else(|| {
                panic!("No item with END_TIME found");
            })
            .unwrap();

        let start_time_min = first_item.START_TIME;
        let start_hour_min = first_item.START_HOUR_KEY.as_ref();
        let start_day_min = first_item.START_DAY_KEY.as_ref();
        let end_time_max = last_item.END_TIME;
        let end_hour_max = last_item.END_HOUR_KEY.as_ref();
        let end_day_max = last_item.END_DAY_KEY.as_ref();

        // Check if we should calculate statistical metrics
        if (testitem_type == P || testitem_type == M) && !test_values.is_empty() {
            Self::new_with_statistics(
                data_source,
                item,
                product,
                product_type,
                product_family,
                is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                retest_bin_num_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                exe_input_cnt,
                exe_pass_cnt,
                exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
                &test_values,
                items,
                now,
            )
        } else {
            Self::new_without_statistics(
                data_source,
                item,
                product,
                product_type,
                product_family,
                is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                retest_bin_num_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                exe_input_cnt,
                exe_pass_cnt,
                exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
            )
        }
    }

    /// Creates a new TestItemSiteBin with statistical calculations
    /// Used when testitem_type is P or M and test_values is not empty
    #[allow(clippy::too_many_arguments)]
    fn new_with_statistics(
        data_source: &str,
        item: &TestItemDetail,
        product: &str,
        product_type: &str,
        product_family: &str,
        is_pass_only: u8,
        sblot_id: &str,
        wafer_no: &str,
        wafer_id_arr: String,
        wafer_lot_id_arr: String,
        units_arr: String,
        origin_units_arr: String,
        lo_limit_arr: String,
        hi_limit_arr: String,
        origin_lo_limit_arr: String,
        origin_hi_limit_arr: String,
        process_arr: String,
        testitem_type_arr: String,
        test_temperature_arr: String,
        tester_name_arr: String,
        tester_type_arr: String,
        prober_handler_typ_arr: String,
        prober_handler_id_arr: String,
        probecard_loadboard_typ_arr: String,
        probecard_loadboard_id_arr: String,
        retest_bin_num_arr: String,
        input_cnt: i32,
        pass_cnt: i32,
        fail_cnt: i32,
        passbin_failingitem_cnt: i32,
        exe_input_cnt: i32,
        exe_pass_cnt: i32,
        exe_fail_cnt: i32,
        start_time_min: Option<i64>,
        start_hour_min: &str,
        start_day_min: &str,
        end_time_max: Option<i64>,
        end_hour_max: &str,
        end_day_max: &str,
        create_hour_key: String,
        create_day_key: String,
        test_values: &[f64],
        items: &Vec<Arc<TestItemDetail>>,
        now: DateTime<Utc>,
    ) -> Self {
        use crate::dto::ads::to_decimal;
        use crate::model::constant::SYSTEM;
        use crate::utils::stats::*;

        let latest_item = items
            .iter()
            .filter(|item| item.START_TIME.is_some())
            .max_by_key(|item| item.START_TIME)
            .ok_or_else(|| {
                log::error!("No item with START_TIME found for HI_LIMIT and LO_LIMIT");
            })
            .unwrap();

        let hi_latest = latest_item.HI_LIMIT;
        let lo_latest = latest_item.LO_LIMIT;
        let himax = hi_latest.unwrap_or(f64::NAN);
        let lomin = lo_latest.unwrap_or(f64::NAN);

        // Calculate basic statistics
        let median = calculate_median(test_values);
        let mean = calculate_mean(test_values);
        let max_val = calculate_max(test_values);
        let min_val = calculate_min(test_values);
        let square_sum = calculate_square_sum(test_values);
        let sum_val = calculate_sum(test_values);
        let std_dev_p = calculate_std_dev(test_values);
        let std_dev_s = if exe_input_cnt > 1 && square_sum.is_some() {
            // TODO 这里scala是没有开根号的
            to_decimal((square_sum.unwrap() / (exe_input_cnt - 1) as f64).sqrt())
        } else {
            to_decimal(0.0)
        };
        let range = if let (Some(max), Some(min)) = (max_val, min_val) { Some(max - min) } else { None };
        let q1 = calculate_q1(test_values);
        let q3 = calculate_q3(test_values);
        let p1 = quantile1(test_values);
        let p5 = quantile5(test_values);
        let p10 = quantile10(test_values);
        let p90 = quantile90(test_values);
        let p95 = quantile95(test_values);
        let p99 = quantile99(test_values);
        let iqr = if let (Some(q3_val), Some(q1_val)) = (q3, q1) { Some(q3_val - q1_val) } else { None };

        // Calculate process capability indices
        let (cp, cpu, cpl, cpk, ca) = if let (Some(mean_val), Some(std_dev_val)) = (mean, std_dev_p) {
            let cp_val = if hi_latest.is_some() && lo_latest.is_some() {
                to_decimal(calculate_cp(std_dev_val, lomin, himax))
            } else {
                None
            };

            let cpu_val =
                if hi_latest.is_some() { to_decimal(calculate_cpu(mean_val, std_dev_val, himax)) } else { None };

            let cpl_val =
                if lo_latest.is_some() { to_decimal(calculate_cpl(mean_val, std_dev_val, lomin)) } else { None };

            let cpk_val = if hi_latest.is_some() && lo_latest.is_some() {
                to_decimal(calculate_cpk(mean_val, std_dev_val, lomin, himax))
            } else {
                None
            };

            let ca_val = if himax.is_nan() || lomin.is_nan() || himax == lomin {
                None
            } else {
                to_decimal(calculate_ca(mean_val, lomin, himax))
            };

            (cp_val, cpu_val, cpl_val, cpk_val, ca_val)
        } else {
            (None, None, None, None, None)
        };

        // Calculate advanced statistics
        let skewness = calculate_skewness(test_values);
        let kurtosis = calculate_kurtosis(test_values);

        // Calculate outlier bounds and filtering
        let (lower_filter, upper_filter, max_wo, min_wo, outlier_cnt) = if let (Some(q1_val), Some(q3_val)) = (q1, q3) {
            let lower_filter = 2.5 * q1_val - 1.5 * q3_val;
            let upper_filter = 2.5 * q3_val - 1.5 * q1_val;
            let max_wo = if test_values.iter().any(|&v| v < upper_filter) {
                test_values
                    .iter()
                    .filter(|&&v| v < upper_filter)
                    .max_by(|a, b| a.partial_cmp(b).unwrap())
                    .copied()
            } else {
                None
            };
            let min_wo = if test_values.iter().any(|&v| v > lower_filter) {
                test_values
                    .iter()
                    .filter(|&&v| v > lower_filter)
                    .min_by(|a, b| a.partial_cmp(b).unwrap())
                    .copied()
            } else {
                None
            };
            let outlier_cnt = test_values.iter().filter(|&&v| v < lower_filter || v > upper_filter).count() as u32;
            (Some(lower_filter), Some(upper_filter), max_wo, min_wo, outlier_cnt)
        } else {
            (None, None, None, None, 0)
        };

        // Calculate normalization parameter
        let nor_parameter = if !himax.is_nan() && !lomin.is_nan() && himax + lomin == 0.0 {
            himax
        } else if himax.is_nan() || lomin.is_nan() {
            f64::NAN
        } else {
            (himax + lomin) / 2.0
        };
        let nor_flag = himax.is_nan() || lomin.is_nan() || nor_parameter.is_nan() || nor_parameter == 0.0;

        // Calculate normalization metrics
        let nor_median =
            if nor_flag || median.is_none() { None } else { Some((median.unwrap() - nor_parameter) / nor_parameter) };
        let nor_mean =
            if nor_flag || mean.is_none() { None } else { Some((mean.unwrap() - nor_parameter) / nor_parameter) };
        let nor_max =
            if nor_flag || max_val.is_none() { None } else { Some((max_val.unwrap() - nor_parameter) / nor_parameter) };
        let nor_min =
            if nor_flag || min_val.is_none() { None } else { Some((min_val.unwrap() - nor_parameter) / nor_parameter) };
        let nor_max_wo_outliers =
            if nor_flag || max_wo.is_none() { None } else { Some((max_wo.unwrap() - nor_parameter) / nor_parameter) };
        let nor_min_wo_outliers =
            if nor_flag || min_wo.is_none() { None } else { Some((min_wo.unwrap() - nor_parameter) / nor_parameter) };
        let nor_iqr =
            if nor_flag || iqr.is_none() { None } else { Some((iqr.unwrap() - nor_parameter) / nor_parameter) };
        let nor_q1 = if nor_flag || q1.is_none() { None } else { Some((q1.unwrap() - nor_parameter) / nor_parameter) };
        let nor_q3 = if nor_flag || q3.is_none() { None } else { Some((q3.unwrap() - nor_parameter) / nor_parameter) };
        let nor_lower = if nor_flag || lower_filter.is_none() {
            None
        } else {
            Some((lower_filter.unwrap() - nor_parameter) / nor_parameter)
        };
        let nor_upper = if nor_flag || upper_filter.is_none() {
            None
        } else {
            Some((upper_filter.unwrap() - nor_parameter) / nor_parameter)
        };

        // Calculate bounds for outlier detection
        let lower = if let (Some(lower_filter_val), max_wo_val) = (lower_filter, max_wo) {
            if let Some(max_wo_val) = max_wo_val {
                if lower_filter_val >= max_wo_val {
                    Some(lower_filter_val)
                } else {
                    Some(max_wo_val)
                }
            } else {
                Some(lower_filter_val)
            }
        } else {
            None
        };
        let upper = if let (Some(upper_filter_val), min_wo_val) = (upper_filter, min_wo) {
            if let Some(min_wo_val) = min_wo_val {
                if upper_filter_val <= min_wo_val {
                    Some(upper_filter_val)
                } else {
                    Some(min_wo_val)
                }
            } else {
                Some(upper_filter_val)
            }
        } else {
            None
        };

        // Create histogram (GROUP_DETAIL)
        let group_detail = group_detail(test_values, range, min_val);

        Self {
            DATA_SOURCE: Arc::from(data_source),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: Arc::from(sblot_id),
            WAFER_ID: Arc::from(wafer_id_arr),
            WAFER_NO: Arc::from(wafer_no),
            WAFER_LOT_ID: Arc::from(wafer_lot_id_arr),
            PRODUCT: Arc::from(product),
            PRODUCT_TYPE: Arc::from(product_type),
            PRODUCT_FAMILY: Arc::from(product_family),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            SITE: item.SITE,
            HBIN: item.HBIN.clone(),
            SBIN: item.SBIN.clone(),
            HBIN_NUM: item.HBIN_NUM,
            HBIN_NAM: item.HBIN_NAM.clone(),
            HBIN_PF: item.HBIN_PF.clone(),
            SBIN_NUM: item.SBIN_NUM,
            SBIN_NAM: item.SBIN_NAM.clone(),
            SBIN_PF: item.SBIN_PF.clone(),
            IS_PASS_ONLY: is_pass_only,
            IS_FINAL: item.IS_FINAL_TEST_IGNORE_TP.unwrap_or(1),
            UNITS_LIST: Arc::from(units_arr.as_str()),
            ORIGIN_UNITS_LIST: Arc::from(origin_units_arr.as_str()),
            LO_LIMIT_LIST: Arc::from(lo_limit_arr.as_str()),
            HI_LIMIT_LIST: Arc::from(hi_limit_arr.as_str()),
            ORIGIN_LO_LIMIT_LIST: Arc::from(origin_lo_limit_arr.as_str()),
            ORIGIN_HI_LIMIT_LIST: Arc::from(origin_hi_limit_arr.as_str()),
            PROCESS_LIST: Arc::from(process_arr.as_str()),
            TESTITEM_TYPE_LIST: Arc::from(testitem_type_arr.as_str()),
            TEST_TEMPERATURE_LIST: Arc::from(test_temperature_arr.as_str()),
            TESTER_NAME_LIST: Arc::from(tester_name_arr.as_str()),
            TESTER_TYPE_LIST: Arc::from(tester_type_arr.as_str()),
            PROBER_HANDLER_TYP_LIST: Arc::from(prober_handler_typ_arr.as_str()),
            PROBER_HANDLER_ID_LIST: Arc::from(prober_handler_id_arr.as_str()),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(probecard_loadboard_typ_arr.as_str()),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(probecard_loadboard_id_arr.as_str()),
            RETEST_BIN_NUM_LIST: Arc::from(retest_bin_num_arr.as_str()),
            INPUT_CNT: input_cnt,
            PASS_CNT: pass_cnt,
            FAIL_CNT: fail_cnt,
            PASSBIN_FAILINGITEM_CNT: passbin_failingitem_cnt,
            EXE_INPUT_CNT: exe_input_cnt,
            EXE_PASS_CNT: exe_pass_cnt,
            EXE_FAIL_CNT: exe_fail_cnt,
            MEDIAN: median.and_then(to_decimal),
            MEAN: mean.and_then(to_decimal),
            MAX: max_val.and_then(to_decimal),
            MIN: min_val.and_then(to_decimal),
            MAX_WO_OUTLIERS: max_wo.and_then(to_decimal),
            MIN_WO_OUTLIERS: min_wo.and_then(to_decimal),
            SUM_SQ: square_sum.and_then(to_decimal),
            SUM_VALUE: sum_val.and_then(to_decimal),
            STDEV_P: std_dev_p.and_then(to_decimal),
            STDEV_S: std_dev_s,
            RANGE: range.and_then(to_decimal),
            IQR: iqr.and_then(to_decimal),
            Q1: q1.and_then(to_decimal),
            Q3: q3.and_then(to_decimal),
            LOWER: lower.and_then(to_decimal),
            UPPER: upper.and_then(to_decimal),
            OUTLIER_CNT: outlier_cnt,
            P1: p1.and_then(to_decimal),
            P5: p5.and_then(to_decimal),
            P10: p10.and_then(to_decimal),
            P90: p90.and_then(to_decimal),
            P95: p95.and_then(to_decimal),
            P99: p99.and_then(to_decimal),
            GROUP_DETAIL: group_detail.into_iter().collect(),
            PP: cp,
            PPU: cpu,
            PPL: cpl,
            PPK: cpk,
            CP: cp,
            CPU: cpu,
            CPL: cpl,
            CPK: cpk,
            CA: ca,
            SKEWNESS: skewness.and_then(to_decimal),
            KURTOSIS: kurtosis.and_then(to_decimal),
            NORMALIZATION_MEDIAN: nor_median.and_then(to_decimal),
            NORMALIZATION_MEAN: nor_mean.and_then(to_decimal),
            NORMALIZATION_MAX: nor_max.and_then(to_decimal),
            NORMALIZATION_MIN: nor_min.and_then(to_decimal),
            NORMALIZATION_MAX_WO_OUTLIERS: nor_max_wo_outliers.and_then(to_decimal),
            NORMALIZATION_MIN_WO_OUTLIERS: nor_min_wo_outliers.and_then(to_decimal),
            NORMALIZATION_IQR: nor_iqr.and_then(to_decimal),
            NORMALIZATION_Q1: nor_q1.and_then(to_decimal),
            NORMALIZATION_Q3: nor_q3.and_then(to_decimal),
            NORMALIZATION_LOWER: nor_lower.and_then(to_decimal),
            NORMALIZATION_UPPER: nor_upper.and_then(to_decimal),
            START_TIME: start_time_min.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            START_HOUR_KEY: Arc::from(start_hour_min),
            START_DAY_KEY: Arc::from(start_day_min),
            END_TIME: end_time_max.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            END_HOUR_KEY: Arc::from(end_hour_max),
            END_DAY_KEY: Arc::from(end_day_max),
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(create_hour_key),
            CREATE_DAY_KEY: Arc::from(create_day_key),
            CREATE_USER: Arc::from(SYSTEM),
            UPLOAD_TIME: if item.UPLOAD_TIME == 0 {
                chrono::DateTime::from_timestamp_millis(item.CREATE_TIME).unwrap_or(now)
            } else {
                chrono::DateTime::from_timestamp_millis(item.UPLOAD_TIME).unwrap_or(now)
            },
            VERSION: item.VERSION,
            IS_DELETE: 0,
        }
    }

    /// Creates a new TestItemSiteBin without statistical calculations
    /// Used when testitem_type is not P or M, or when test_values is empty
    #[allow(clippy::too_many_arguments)]
    fn new_without_statistics(
        data_source: &str,
        item: &TestItemDetail,
        product: &str,
        product_type: &str,
        product_family: &str,
        is_pass_only: u8,
        sblot_id: &str,
        wafer_no: &str,
        wafer_id_arr: String,
        wafer_lot_id_arr: String,
        units_arr: String,
        origin_units_arr: String,
        lo_limit_arr: String,
        hi_limit_arr: String,
        origin_lo_limit_arr: String,
        origin_hi_limit_arr: String,
        process_arr: String,
        testitem_type_arr: String,
        test_temperature_arr: String,
        tester_name_arr: String,
        tester_type_arr: String,
        prober_handler_typ_arr: String,
        prober_handler_id_arr: String,
        probecard_loadboard_typ_arr: String,
        probecard_loadboard_id_arr: String,
        retest_bin_num_arr: String,
        input_cnt: i32,
        pass_cnt: i32,
        fail_cnt: i32,
        passbin_failingitem_cnt: i32,
        exe_input_cnt: i32,
        exe_pass_cnt: i32,
        exe_fail_cnt: i32,
        start_time_min: Option<i64>,
        start_hour_min: &str,
        start_day_min: &str,
        end_time_max: Option<i64>,
        end_hour_max: &str,
        end_day_max: &str,
        create_hour_key: String,
        create_day_key: String,
    ) -> Self {
        use crate::model::constant::SYSTEM;

        let now = Utc::now();

        Self {
            DATA_SOURCE: Arc::from(data_source),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: Arc::from(sblot_id),
            WAFER_ID: Arc::from(wafer_id_arr.as_str()),
            WAFER_NO: Arc::from(wafer_no),
            WAFER_LOT_ID: Arc::from(wafer_lot_id_arr.as_str()),
            PRODUCT: Arc::from(product),
            PRODUCT_TYPE: Arc::from(product_type),
            PRODUCT_FAMILY: Arc::from(product_family),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            SITE: item.SITE,
            HBIN: item.HBIN.clone(),
            SBIN: item.SBIN.clone(),
            HBIN_NUM: item.HBIN_NUM,
            HBIN_NAM: item.HBIN_NAM.clone(),
            HBIN_PF: item.HBIN_PF.clone(),
            SBIN_NUM: item.SBIN_NUM,
            SBIN_NAM: item.SBIN_NAM.clone(),
            SBIN_PF: item.SBIN_PF.clone(),
            IS_PASS_ONLY: is_pass_only,
            IS_FINAL: item.IS_FINAL_TEST_IGNORE_TP.unwrap_or(1),
            UNITS_LIST: Arc::from(units_arr.as_str()),
            ORIGIN_UNITS_LIST: Arc::from(origin_units_arr.as_str()),
            LO_LIMIT_LIST: Arc::from(lo_limit_arr.as_str()),
            HI_LIMIT_LIST: Arc::from(hi_limit_arr.as_str()),
            ORIGIN_LO_LIMIT_LIST: Arc::from(origin_lo_limit_arr.as_str()),
            ORIGIN_HI_LIMIT_LIST: Arc::from(origin_hi_limit_arr.as_str()),
            PROCESS_LIST: Arc::from(process_arr.as_str()),
            TESTITEM_TYPE_LIST: Arc::from(testitem_type_arr.as_str()),
            TEST_TEMPERATURE_LIST: Arc::from(test_temperature_arr.as_str()),
            TESTER_NAME_LIST: Arc::from(tester_name_arr.as_str()),
            TESTER_TYPE_LIST: Arc::from(tester_type_arr.as_str()),
            PROBER_HANDLER_TYP_LIST: Arc::from(prober_handler_typ_arr.as_str()),
            PROBER_HANDLER_ID_LIST: Arc::from(prober_handler_id_arr.as_str()),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(probecard_loadboard_typ_arr.as_str()),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(probecard_loadboard_id_arr.as_str()),
            RETEST_BIN_NUM_LIST: Arc::from(retest_bin_num_arr.as_str()),
            INPUT_CNT: input_cnt,
            PASS_CNT: pass_cnt,
            FAIL_CNT: fail_cnt,
            PASSBIN_FAILINGITEM_CNT: passbin_failingitem_cnt,
            EXE_INPUT_CNT: exe_input_cnt,
            EXE_PASS_CNT: exe_pass_cnt,
            EXE_FAIL_CNT: exe_fail_cnt,
            // All statistical fields are None for non-statistical types
            MEDIAN: None,
            MEAN: None,
            MAX: None,
            MIN: None,
            MAX_WO_OUTLIERS: None,
            MIN_WO_OUTLIERS: None,
            SUM_SQ: None,
            SUM_VALUE: None,
            STDEV_P: None,
            STDEV_S: None,
            RANGE: None,
            IQR: None,
            Q1: None,
            Q3: None,
            LOWER: None,
            UPPER: None,
            OUTLIER_CNT: 0,
            P1: None,
            P5: None,
            P10: None,
            P90: None,
            P95: None,
            P99: None,
            GROUP_DETAIL: Vec::new(),
            PP: None,
            PPU: None,
            PPL: None,
            PPK: None,
            CP: None,
            CPU: None,
            CPL: None,
            CPK: None,
            CA: None,
            SKEWNESS: None,
            KURTOSIS: None,
            NORMALIZATION_MEDIAN: None,
            NORMALIZATION_MEAN: None,
            NORMALIZATION_MAX: None,
            NORMALIZATION_MIN: None,
            NORMALIZATION_MAX_WO_OUTLIERS: None,
            NORMALIZATION_MIN_WO_OUTLIERS: None,
            NORMALIZATION_IQR: None,
            NORMALIZATION_Q1: None,
            NORMALIZATION_Q3: None,
            NORMALIZATION_LOWER: None,
            NORMALIZATION_UPPER: None,
            START_TIME: start_time_min.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            START_HOUR_KEY: Arc::from(start_hour_min),
            START_DAY_KEY: Arc::from(start_day_min),
            END_TIME: end_time_max.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            END_HOUR_KEY: Arc::from(end_hour_max),
            END_DAY_KEY: Arc::from(end_day_max),
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(create_hour_key.as_str()),
            CREATE_DAY_KEY: Arc::from(create_day_key.as_str()),
            CREATE_USER: Arc::from(SYSTEM),
            UPLOAD_TIME: if item.UPLOAD_TIME == 0 {
                chrono::DateTime::from_timestamp_millis(item.CREATE_TIME).unwrap_or(now)
            } else {
                chrono::DateTime::from_timestamp_millis(item.UPLOAD_TIME).unwrap_or(now)
            },
            VERSION: item.VERSION,
            IS_DELETE: 0,
        }
    }
}
