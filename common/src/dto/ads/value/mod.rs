// ADS value objects representing aggregated test item results
// These structures contain statistical metrics and aggregated data
pub mod die_final_info;
pub mod test_item_bin;
pub mod test_item_detail;
pub mod test_item_program;
pub mod test_item_site;
pub mod test_item_site_bin;

use crate::model::constant::ZERO;
use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
pub use die_final_info::DieFinalInfo;
use std::collections::HashMap;
pub use test_item_bin::TestItemBin;
pub use test_item_program::TestItemProgram;
pub use test_item_site::TestItemSite;
pub use test_item_site_bin::TestItemSiteBin;

pub fn to_decimal(val: f64) -> Option<Decimal38_18> {
    if val.is_finite() && !val.is_nan() {
        Some(val.into_decimal38_18())
    } else {
        None
    }
}

// TODO 排序
pub fn mk_string_distinct(values: Vec<&str>) -> String {
    let unique: std::collections::HashSet<&str> = values.into_iter().collect();
    unique.into_iter().collect::<Vec<_>>().join(",")
}

pub fn group_detail(test_values: &[f64], range: Option<f64>, min_val: Option<f64>) -> HashMap<String, u32> {
    if let (Some(range_val), Some(min_val_val)) = (range, min_val) {
        if range_val == 0.0 {
            let mut map = HashMap::new();
            map.insert(ZERO.to_string(), test_values.len() as u32);
            map
        } else {
            let mut map = HashMap::new();
            for &value in test_values {
                let idx = ((value - min_val_val) / (range_val / 100.0)).floor() as i32;
                let bucket = if idx > 99 {
                    "99"
                } else if idx < 0 {
                    ZERO
                } else {
                    &idx.to_string()
                };
                *map.entry(bucket.to_string()).or_insert(0) += 1;
            }
            map
        }
    } else {
        HashMap::new()
    }
}
