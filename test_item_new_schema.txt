INSERT INTO MY_TABLE(DATA_SOURCE, 
UPLOAD_TYPE, 
CUSTOMER, 
SUB_CUSTOMER, 
FAB, 
FAB_SITE, 
FACTORY, 
FACTORY_SITE, 
TEST_AREA, 
TEST_STAGE, 
DEVICE_ID, 
LOT_TYPE, 
LOT_ID, 
S<PERSON>OT_ID, 
WAFER_ID, 
WAFER_NO, 
WAFER_LOT_ID, 
PRODUCT, 
PRODUCT_TYPE, 
PRODUCT_FAMILY, 
TEST_PROGRAM, 
TEST_PROGRAM_VERSION, 
TEST_NUM, 
TEST_TXT, 
TEST_ITEM, 
SITE, 
HBIN, 
SBIN, 
HBIN_NUM, 
HBIN_NAM, 
HBIN_PF, 
SBIN_NUM, 
SBIN_NAM, 
SBIN_PF, 
IS_PASS_ONLY, 
IS_FINAL, 
UNITS_LIST, 
ORIGIN_UNITS_LIST, 
LO_LIMIT_LIST, 
HI_LIMIT_LIST, 
ORIGIN_LO_LIMIT_LIST, 
ORIGIN_HI_LIMIT_LIST, 
PROCESS_LIST, 
TESTITEM_TYPE_LIST, 
TEST_TEMPERATURE_LIST, 
TESTER_NAME_LIST, 
TESTER_TYPE_LIST, 
PROBER_HANDLER_TYP_LIST, 
PROBER_HANDLER_ID_LIST, 
PROBECARD_LOADBOARD_TYP_LIST, 
PROBECARD_LOADBOARD_ID_LIST, 
RETEST_BIN_NUM_LIST, 
INPUT_CNT, 
PASS_CNT, 
FAIL_CNT, 
PASSBIN_FAILINGITEM_CNT, 
EXE_INPUT_CNT, 
EXE_PASS_CNT, 
EXE_FAIL_CNT, 
MEDIAN, 
MEAN, 
MAX, 
MIN, 
MAX_WO_OUTLIERS, 
MIN_WO_OUTLIERS, 
SUM_SQ, 
SUM_VALUE, 
STDEV_P, 
STDEV_S, 
RANGE, 
IQR, 
Q1, 
Q3, 
LOWER, 
UPPER, 
OUTLIER_CNT, 
P1, 
P5, 
P10, 
P90, 
P95, 
P99, 
GROUP_DETAIL, 
PP, 
PPU, 
PPL, 
PPK, 
CP, 
CPU, 
CPL, 
CPK, 
CA, 
SKEWNESS, 
KURTOSIS, 
NORMALIZATION_MEDIAN, 
NORMALIZATION_MEAN, 
NORMALIZATION_MAX, 
NORMALIZATION_MIN, 
NORMALIZATION_MAX_WO_OUTLIERS, 
NORMALIZATION_MIN_WO_OUTLIERS, 
NORMALIZATION_IQR, 
NORMALIZATION_Q1, 
NORMALIZATION_Q3, 
NORMALIZATION_LOWER, 
NORMALIZATION_UPPER, 
START_TIME, 
START_HOUR_KEY, 
START_DAY_KEY, 
END_TIME, 
END_HOUR_KEY, 
END_DAY_KEY, 
CREATE_TIME, 
CREATE_HOUR_KEY, 
CREATE_DAY_KEY, 
CREATE_USER, 
UPLOAD_TIME, 
VERSION, 
IS_DELETE) VALUES ('Test Raw Data', 
'AUTO', 
'YEESTOR', 
'YEESTOR', 
'', 
'', 
'LEADYO', 
'LEADYO', 
'CP', 
'CP1', 
'YS8293ENAB', 
'PRODUCTION', 
'ENF083', 
'', 
'ENF083_25', 
'25', 
'ENF083', 
'YS8293ENAB', 
'Production', 
'family1', 
'YS8293ENAB_6SC_V19.pln', 
'YS8293ENAB_6SC_V19.pln', 
1000001000, 
'OS_Test JUDGE_V_PPMU-FALE', 
'1000001000:OS_Test JUDGE_V_PPMU-FALE', 
0, 
'HBIN18-Leakage_Test_4_FAIL', 
'SBIN18-Leakage_Test_4_FAIL', 
18, 
'Leakage_Test_4_FAIL', 
'F', 
18, 
'Leakage_Test_4_FAIL', 
'F', 
0, 
0, 
'V', 
'V', 
'-1.2', 
'-0.2', 
'-1.2', 
'-0.2', 
'', 
'M', 
'000.0', 
'3380D-0040', 
'3380D', 
'', 
'A07', 
'', 
'GG-HLN-026', 
'ALL', 
142, 
142, 
0, 
0, 
142, 
142, 
0, 
-0.420759140000000000, 
-0.420091568450704500, 
-0.414672730000000000, 
-0.423954520000000000, 
-0.416650830000000000, 
-0.423954520000000000, 
25.060233430973280000, 
-59.653002720000030000, 
0.001895055713556953, 
0.421582912358551700, 
0.009281790000000012, 
0.001978069999999998, 
-0.421519940000000000, 
-0.419541870000000000, 
-0.416650830000000000, 
-0.423954520000000000, 
12, 
-0.422729626100000000, 
-0.421976420000000000, 
-0.421824280000000000, 
-0.416818186999999950, 
-0.416194350000000000, 
-0.415129200000000000, 
{'37': 6, 
'42': 3, 
'83': 3, 
'52': 4, 
'86': 2, 
'59': 1, 
'68': 1, 
'63': 2, 
'81': 2, 
'47': 3, 
'45': 3, 
'44': 2, 
'0': 2, 
'55': 1, 
'72': 3, 
'75': 1, 
'26': 13, 
'39': 10, 
'27': 4, 
'90': 1, 
'77': 1, 
'24': 6, 
'34': 8, 
'49': 1, 
'21': 6, 
'57': 1, 
'36': 2, 
'73': 2, 
'22': 7, 
'31': 7, 
'60': 1, 
'65': 1, 
'78': 2, 
'95': 3, 
'29': 3, 
'18': 1, 
'19': 3, 
'9': 1, 
'32': 13, 
'40': 6}, 
87.948162000000000000, 
38.713298000000000000, 
137.183026000000000000, 
38.713298000000000000, 
87.948162000000000000, 
38.713298000000000000, 
137.183026000000000000, 
38.713298000000000000, 
0.559817000000000000, 
1.146246893016522400, 
0.562610088314224200, 
-0.398915514285714260, 
-0.399869187927565000, 
-0.407610385714285700, 
-0.394350685714285660, 
-0.404784528571428500, 
-0.394350685714285660, 
-1.002825814285714300, 
-0.397828657142857140, 
-0.400654471428571400, 
-0.393589935714285800, 
-0.404893192857143000, 
'2023-11-05 07:22:45', 
'2023110507', 
'20231105', 
'2023-11-05 11:38:46', 
'2023110511', 
'20231105', 
'2025-08-19 16:00:04', 
'2025081916', 
'20250819', 
'System', 
'2025-07-08 19:55:54', 
1755590396841, 
0);
